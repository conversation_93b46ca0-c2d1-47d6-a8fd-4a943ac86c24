'use client';

import { module<PERSON>pi } from '@/api/moduleApi';
import { approvalFlowApi } from '@/api/approvalFlowApi';
import PageBreadcrumb from '@/components/common/PageBreadCrumb';
import { Dropdown } from '@/components/ui/dropdown/Dropdown';
import { DropdownItem } from '@/components/ui/dropdown/DropdownItem';
import { Modal } from '@/components/ui/modal';
import { Table, TableHeader, TableBody, TableRow, TableCell } from '@/components/ui/table';

import { ModuleApp, CreateFlowRequest, Step, UpdateFlowRequest, AdditionInfoSetting } from '@/types';
import { useEffect, useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaExclamation, FaEllipsisV, FaCheckCircle, FaPauseCircle, FaTh, FaList, FaTable, FaCompressArrowsAlt, FaMinus, FaCog, FaEllipsisH } from 'react-icons/fa';
import { toast } from 'react-toastify';
import SearchBar from '@/components/ui/search/SearchBar';
import Button from '@/components/ui/button/Button';
import './page.module.scss';
import '@/styles/approval-flow.css';
import Select from '@/components/form/Select';
import { ChevronDownIcon } from '@/icons';
import { GrTableAdd } from "react-icons/gr";
import AdditionalInfoTable from '@/components/tables/AdditionalInfoTable';
import { TOAST_ID } from '@/constants/toastIds';
import Image from 'next/image';

type ViewMode = 'grid' | 'table' | 'list' | 'compact';

export default function ModuleManagementPage() {
  const [appItems, setAppItems] = useState<ModuleApp[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showApprovalFlowModal, setShowApprovalFlowModal] = useState(false);
  const [showAdditionalInfoModal, setShowAdditionalInfoModal] = useState(false);
  const [search, setSearch] = useState('');
  const [selectedItem, setSelectedItem] = useState<ModuleApp | null>(null);
  const [isOpenOption, setIsOpenOption] = useState(false);
  const [openIndex, setOpenIndex] = useState(-1);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, left: 0 });
  const [viewMode, setViewMode] = useState<ViewMode>('grid');


  // Form states
  const [itemName, setItemName] = useState('');
  const [description, setDescription] = useState('');
  const [enabled, setEnabled] = useState(true);

  // Approval flow states
  const [flowName, setFlowName] = useState('');
  const [flowDescription, setFlowDescription] = useState('');
  const [isLoadingFlow, setIsLoadingFlow] = useState(false);
  const [stepApprovalIndex, setStepApprovalIndex] = useState<number>(0);
  const [stepApproverIndex, setStepApproverIndex] = useState<number>(0);
  const [existingFlowId, setExistingFlowId] = useState<number | null>(null);
  const approverTypeOptions = [
    { value: "UserApprove", label: "User chỉ định" },
    { value: "DirectManagerApprove", label: "Quản lý trực tiếp" },
    { value: "CreatorApprove", label: "Người tạo ticket" },
  ];

  const [approvalSteps, setApprovalSteps] = useState<Step[]>([
    {
      flowId: 0,
      stepOrder: 1,
      name: '',
      stepApprovers: [
        {
          id: 0,
          stepId: 0,
          userId: '',
          type: approverTypeOptions[0].value,
          name: '',
          additionInfoSettings: []
        }
      ]
    }
  ]);


  
  const fetchAppItems = async () => {
    try {
      const items = await moduleApi.getModules(true, 1000, 0);
      if (items != null) {
        setAppItems(items);
      }
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    fetchAppItems();
  }, []);

  const toggleDropdown = (e: any, index: number) => {
    e.preventDefault();
    const rect = e.currentTarget.getBoundingClientRect();
    setDropdownPosition({
      top: rect.top + rect.height,
      right: rect.right,
      left: rect.left
    });
    setOpenIndex(index);
    setIsOpenOption(true);
  };

  function closeDropdown() {
    setIsOpenOption(false);
  }

  const handleDelete = async () => {
    if (openIndex > -1) {
      try {
        const appItem = appItems[openIndex];
        if (appItem) {
          // Assuming there's a delete endpoint in the future
          const res = await moduleApi.deleteModule(appItem.id);
          if (res) {
            toast.success("Xóa module thành công!");
            fetchAppItems();
          } else {
            toast.error("Xóa module thất bại!");
          }
          // toast.error("Chức năng xóa chưa được triển khai!");
        }
      } catch (err) {
        console.log(err);
      }
      
      setOpenIndex(-1);
      setIsOpenOption(false);
      setShowDeleteModal(false);
    }
  };

  const handleCreateItem = async () => {
    if(itemName == null || itemName.length == 0)
    {
      toast.warning("Vui lòng nhập tên Nghiệp vụ!", {toastId: TOAST_ID.MISSING_MODULE_NAME});
      return;
    }
    try {
      const res = await moduleApi.addModule(itemName, description);
      if (res) {
        toast.success("Thêm module thành công!");
        fetchAppItems();
        setShowCreateModal(false);
        resetForm();
      }
    } catch (err) {
      console.log(err);
      // toast.error("Thêm module thất bại!");
    }
  };

  const handleUpdateItem = async () => {
    if (selectedItem) {
      if(itemName == null || itemName.length == 0)
      {
        toast.warning("Vui lòng nhập tên Nghiệp vụ!", {toastId: TOAST_ID.MISSING_MODULE_NAME});
        return;
      }
      try {
        const res = await moduleApi.updateModule(
          selectedItem.id,
          itemName,
          description,
          enabled
        );
        if (res) {
          toast.success("Cập nhật module thành công!");
          fetchAppItems();
          setShowUpdateModal(false);
          resetForm();
        }
      } catch (err) {
        console.log(err);
        // toast.error("Cập nhật module thất bại!");
      }
    }
  };

  const onClickEdit = () => {
    if (openIndex > -1) {
      const appItem = appItems[openIndex];
      setSelectedItem(appItem);
      setItemName(appItem.name);
      setDescription(appItem.description || '');
      setEnabled(appItem.enabled);
      setShowUpdateModal(true);
      setOpenIndex(-1);
      setIsOpenOption(false);
    }
  };

  const onClickApprovalFlow = async () => {
    if (openIndex > -1) {
      const appItem = appItems[openIndex];
      setSelectedItem(appItem);
      setIsLoadingFlow(true);

      if(appItem.approvalFlowId)
      {
        try {
          // Try to load existing flow detail
          const existingFlow = await approvalFlowApi.getFlowDetail(appItem.id);
          
          if (existingFlow) {
            // Load existing flow data
            setExistingFlowId(existingFlow.id);
            setFlowName(existingFlow.name);
            setFlowDescription(existingFlow.description);
            setApprovalSteps(existingFlow.steps);
          } else {
            // Set default values for new flow
            setExistingFlowId(null);
            setFlowName(`Approval Flow cho ${appItem.name}`);
            setFlowDescription(`Quy trình phê duyệt cho module ${appItem.name}`);
            resetApprovalFlowForm();
          }
        } catch (error) {
          console.log("Error loading flow detail:", error);
          // Set default values if error
          setExistingFlowId(null);
          setFlowName(`Approval Flow cho ${appItem.name}`);
          setFlowDescription(`Quy trình phê duyệt cho module ${appItem.name}`);
          resetApprovalFlowForm();
        } finally {
          setIsLoadingFlow(false);
        }
      }
      else{
        setIsLoadingFlow(false);
      }

      setShowApprovalFlowModal(true);
      // setOpenIndex(-1);
      setIsOpenOption(false);
    }
  };

  const onClickAdditionalInfo = async (stepIndex: number, approverIndex: number) => {
    const appItem = appItems[openIndex];
    setSelectedItem(appItem);
    setIsLoadingFlow(true);
    setStepApprovalIndex(stepIndex)
    setStepApproverIndex(approverIndex)

    try {
      
    } catch (error) {
      console.log("Error loading flow detail:", error);
      
    } finally {
      setIsLoadingFlow(false);
    }
    setShowAdditionalInfoModal(true);
    setIsOpenOption(false);
  };

  const resetForm = () => {
    setItemName('');
    setDescription('');
    setEnabled(true);
    setSelectedItem(null);
  };

  const resetApprovalFlowForm = () => {
    setFlowName('');
    setFlowDescription('');
    setExistingFlowId(null);
    setIsLoadingFlow(false);
    setApprovalSteps([
      {
        flowId: 0,
        stepOrder: 1,
        name: '',
        stepApprovers: [
          {
            id: 0,
            stepId: 0,
            userId: '',
            type: 'user',
            name: '',
            additionInfoSettings: []
          }
        ]
      }
    ]);
  };

  // Approval steps management functions
  const addApprovalStep = () => {
    const newStep: Step = {
      flowId: 0,
      stepOrder: approvalSteps.length + 1,
      name: '',
      stepApprovers: [
        {
          id: 0,
          stepId: 0,
          userId: '',
          type: 'user',
          name: '',
          additionInfoSettings: []
        }
      ]
    };
    setApprovalSteps([...approvalSteps, newStep]);
  };

  const removeApprovalStep = (index: number) => {
    if (approvalSteps.length > 1) {
      const newSteps = approvalSteps.filter((_, i) => i !== index);
      // Update step orders
      const updatedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1
      }));
      setApprovalSteps(updatedSteps);
    }
  };

  const updateApprovalStep = (index: number, field: string, value: string) => {
    const newSteps = [...approvalSteps];
    if (field === 'name') {
      newSteps[index].name = value;
    }
    setApprovalSteps(newSteps);
  };

  const updateStepApprover = (stepIndex: number, approverIndex: number, field: string, value: string) => {
    const newSteps = [...approvalSteps];
    if (field === 'userId') {
      newSteps[stepIndex].stepApprovers[approverIndex].userId = value;
    } else if (field === 'name') {
      newSteps[stepIndex].stepApprovers[approverIndex].name = value;
    } else if (field === 'type') {
      newSteps[stepIndex].stepApprovers[approverIndex].type = value;
    }
    setApprovalSteps(newSteps);
  };

  const addApprover = (stepIndex: number) => {
    const newSteps = [...approvalSteps];
    newSteps[stepIndex].stepApprovers.push({
      id: 0,
      stepId: 0,
      userId: '',
      type: 'user',
      name: '',
      additionInfoSettings: []
    });
    setApprovalSteps(newSteps);
  };

  const removeApprover = (stepIndex: number, approverIndex: number) => {
    const newSteps = [...approvalSteps];
    if (newSteps[stepIndex].stepApprovers.length > 1) {
      newSteps[stepIndex].stepApprovers.splice(approverIndex, 1);
      setApprovalSteps(newSteps);
    }
  };

  const handleApprovalFlowSubmit = async () => {
    if (!selectedItem) return;

    try {
      // Validate approval steps
      const hasValidSteps = approvalSteps.every(step =>
        step.name.trim() !== '' &&
        step.stepApprovers.every(approver =>
          approver.name.trim() !== ''
        )
      );

      if (!hasValidSteps) {
        toast.error("Vui lòng điền đầy đủ thông tin các bước phê duyệt!", {toastId: TOAST_ID.MISSING_APPROVAL_STEP});
        return;
      }

      if (existingFlowId) {
        // Tạo approval flow request
        const approvalFlowRequest: UpdateFlowRequest = {
          id: existingFlowId,
          name: flowName,
          description: flowDescription,
          moduleId: selectedItem.id,
          steps: approvalSteps.map(step => ({
            ...step,
            flowId: existingFlowId || 0 // Sử dụng existing flow ID nếu có
          }))
        };
        await approvalFlowApi.updateFlow(approvalFlowRequest);
        toast.success("Cập nhật quy trình phê duyệt thành công!");
      } else {
        // Tạo approval flow request
      const approvalFlowRequest: CreateFlowRequest = {
        name: flowName,
        description: flowDescription,
        moduleId: selectedItem.id,
        steps: approvalSteps.map(step => ({
          ...step,
          flowId: existingFlowId || 0 // Sử dụng existing flow ID nếu có
        }))
      };
        await approvalFlowApi.addFlow(approvalFlowRequest);
        toast.success("Tạo quy trình phê duyệt thành công!");
      }

      setShowApprovalFlowModal(false);
      resetApprovalFlowForm();
      fetchAppItems(); // Refresh để cập nhật approvalFlowId
    } catch (err) {
      console.log("Lỗi xử lý approval flow:", err);
      if (existingFlowId) {
        toast.error("Không thể cập nhật quy trình phê duyệt!");
      } else {
        toast.error("Không thể tạo quy trình phê duyệt!");
      }
    }
  };

  // Filter items based on search
  const filteredItems = appItems.filter((item) =>
    item.name.toLowerCase().includes(search.toLowerCase()) || search.length === 0
  );

  // Render Grid View
  const renderGridView = () => (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6'>
      {/* {filteredItems.map((item, index) => (
        <div key={index} className="group p-5 bg-white border border-gray-100 hover:border-gray-300 rounded-xl shadow-sm cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-sky-100">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center">
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-3 transition-all duration-300 ${item.enabled ? 'bg-gradient-primary shadow-lg' : 'bg-gray-200'}`} style={item.enabled ? {boxShadow: '0 10px 15px rgba(55, 82, 216, 0.3)'} : {}}>
                {item.enabled ? <FaCheckCircle className='h-6 w-6 text-white' /> : <FaPauseCircle className='h-6 w-6 text-gray-600' />}
              </div>
              <div>
                <h3 className="font-bold text-gray-800 group-hover:text-sky-700 transition-colors duration-300">{item.name}</h3>
                <span className={`text-xs px-2 py-1 rounded-full transition-all duration-300 ${item.enabled ? 'bg-sky-100 text-sky-800' : 'bg-gray-100 text-gray-800'}`}>
                  {item.enabled ? 'Đang hoạt động' : 'Tạm dừng'}
                </span>
              </div>
            </div>
            <button
              className="text-gray-400 hover:text-sky-600 transition-all duration-300 p-2 rounded-lg hover:bg-sky-50"
              onClick={(e) => toggleDropdown(e, index)}
            >
              <FaEllipsisV />
            </button>
          </div>
          <p className="text-gray-600 text-sm mb-4 group-hover:text-gray-700 transition-colors duration-300">
            {item.description || 'Không có mô tả'}
          </p>
        </div>
      ))} */}
      {filteredItems.map((item, index) => {
        let imageIndex = index % 15 + 9;

        return <div key={index} className="group p-3 bg-white dark:bg-gray-700 dark:border-gray-600 border border-gray-100 hover:border-gray-300 rounded-xl shadow-sm cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-sky-100">
          <div className="flex items-start justify-between mb-4">
            <div className="items-center w-full">
              <div className='h-28 overflow-hidden rounded-lg relative'>
                <Image
                  src={`/agent-ai/images/gradient/${imageIndex}.jfif`}
                  alt="ai"
                  width={500}
                  height={120}
                  className='h-full object-fill w-full'
                /> 
                <h3 className="absolute top-[50%] left-[50%] text-2xl transform -translate-x-1/2 -translate-y-1/2 font-medium text-gray-600">{item.name}</h3>
              </div>
              <div className='flex justify-between pt-3 w-full overflow-hidden'>
                <h3 className="font-medium text-gray-800 group-hover:text-sky-700 transition-colors duration-300 text-lg w-full overflow-hidden line-clamp-2 dark:text-white">{item.name}</h3>
                <button
                  className="text-gray-400 hover:text-sky-600 transition-all duration-300 p-2 rounded-lg hover:bg-sky-50"
                  onClick={(e) => toggleDropdown(e, index)}
                >
                  <FaEllipsisH />
                </button>
              </div>
              <h3 className="font-light text-gray-500 group-hover:text-sky-700 transition-colors duration-300 pb-2 h-11.5 overflow-hidden line-clamp-2 dark:text-gray-300">{item.description}</h3>
              <span className={`text-xs px-2 py-1 rounded-full transition-all duration-300 ${item.enabled ? 'bg-sky-100 dark:bg-sky-800 text-sky-800 dark:text-sky-50' : 'bg-gray-100 text-gray-800'}`}>
                  {item.enabled ? 'Đang hoạt động' : 'Tạm dừng'}
                </span>
            </div>
          </div>
        </div>
      })}
    </div>
  );

  // Render Table View
  const renderTableView = () => (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white">
      <div className="max-w-full overflow-x-auto">
        <Table>
          <TableHeader className="border-b border-gray-100">
            <TableRow>
              <TableCell isHeader className="px-6 py-4 font-medium text-gray-500 text-left text-sm">
                Module
              </TableCell>
              <TableCell isHeader className="px-6 py-4 font-medium text-gray-500 text-left text-sm">
                Mô tả
              </TableCell>
              <TableCell isHeader className="px-6 py-4 font-medium text-gray-500 text-left text-sm">
                Trạng thái
              </TableCell>
              <TableCell isHeader className="px-6 py-4 font-medium text-gray-500 text-left text-sm">
                Ngày tạo
              </TableCell>
              <TableCell isHeader className="px-6 py-4 font-medium text-gray-500 text-left text-sm">
                Thao tác
              </TableCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map((item, index) => (
              <TableRow key={index} className="border-b border-gray-50 hover:bg-gray-50 transition-colors">
                <TableCell className="px-6 py-4">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3 ${item.enabled ? 'bg-gradient-primary' : 'bg-gray-200'}`}>
                      {item.enabled ? <FaCheckCircle className='h-5 w-5 text-white' /> : <FaPauseCircle className='h-5 w-5 text-gray-600' />}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{item.name}</div>
                      <div className="text-sm text-gray-500">ID: {item.id}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="px-6 py-4">
                  <div className="text-sm text-gray-900 max-w-xs truncate">
                    {item.description || 'Không có mô tả'}
                  </div>
                </TableCell>
                <TableCell className="px-6 py-4">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    item.enabled
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {item.enabled ? 'Hoạt động' : 'Tạm dừng'}
                  </span>
                </TableCell>
                <TableCell className="px-6 py-4 text-sm text-gray-500">
                  {new Date(item.createdAt).toLocaleDateString('vi-VN')}
                </TableCell>
                <TableCell className="px-6 py-4">
                  <button
                    className="text-gray-400 hover:text-sky-600 transition-all duration-300 p-2 rounded-lg hover:bg-sky-50"
                    onClick={(e) => toggleDropdown(e, index)}
                  >
                    <FaEllipsisV />
                  </button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );

  // Render List View
  const renderListView = () => (
    <div className="space-y-4">
      {filteredItems.map((item, index) => (
        <div key={index} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-all duration-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={`w-14 h-14 rounded-xl flex items-center justify-center ${item.enabled ? 'bg-gradient-primary shadow-lg' : 'bg-gray-200'}`}>
                {item.enabled ? <FaCheckCircle className='h-7 w-7 text-white' /> : <FaPauseCircle className='h-7 w-7 text-gray-600' />}
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
                  <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                    item.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {item.enabled ? 'Hoạt động' : 'Tạm dừng'}
                  </span>
                </div>
                <p className="text-gray-600 mt-1">{item.description || 'Không có mô tả'}</p>
                <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                  <span>ID: {item.id}</span>
                  <span>•</span>
                  <span>Tạo: {new Date(item.createdAt).toLocaleDateString('vi-VN')}</span>
                  <span>•</span>
                  <span>Bởi: {item.createdBy}</span>
                </div>
              </div>
            </div>
            <button
              className="text-gray-400 hover:text-sky-600 transition-all duration-300 p-3 rounded-lg hover:bg-sky-50"
              onClick={(e) => toggleDropdown(e, index)}
            >
              <FaEllipsisV />
            </button>
          </div>
        </div>
      ))}
    </div>
  );

  // Render Compact View
  const renderCompactView = () => (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-3">
      {filteredItems.map((item, index) => (
        <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-300 group">
          <div className="flex items-center justify-between mb-2">
            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${item.enabled ? 'bg-gradient-primary' : 'bg-gray-200'}`}>
              {item.enabled ? <FaCheckCircle className='h-4 w-4 text-white' /> : <FaPauseCircle className='h-4 w-4 text-gray-600' />}
            </div>
            <button
              className="text-gray-400 hover:text-sky-600 transition-all duration-300 p-1 rounded hover:bg-sky-50 opacity-0 group-hover:opacity-100"
              onClick={(e) => toggleDropdown(e, index)}
            >
              <FaEllipsisV className="h-3 w-3" />
            </button>
          </div>
          <h4 className="font-medium text-gray-900 text-sm truncate mb-1">{item.name}</h4>
          <p className="text-xs text-gray-500 line-clamp-2">{item.description || 'Không có mô tả'}</p>
          <div className="mt-2">
            <span className={`inline-block px-2 py-1 text-xs rounded-full ${
              item.enabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
            }`}>
              {item.enabled ? 'Hoạt động' : 'Tạm dừng'}
            </span>
          </div>
        </div>
      ))}
    </div>
  );

  // Render content based on view mode
  const renderContent = () => {
    switch (viewMode) {
      case 'table':
        return renderTableView();
      case 'list':
        return renderListView();
      case 'compact':
        return renderCompactView();
      default:
        return renderGridView();
    }
  };

  return (
    <div className="">
      <PageBreadcrumb pageTitle="Quản lý Nghiệp vụ" />
      <div className="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]">
        <div className='p-6 flex justify-between items-center border-b border-gray-200'>
            <div className='flex gap-6 items-center'>
              <Button
                onClick={() => setShowCreateModal(true)}
                className='flex items-center p-2 px-6 text-white font-semibold transition-all rounded-md gap-2 cursor-pointer'
              >
                <FaPlus className="mr-2" />
                Thêm Nghiệp vụ
              </Button>

              {/* View Mode Selector */}
              <div className="flex items-center gap-2 bg-gray-100 rounded-lg p-1 h-10 dark:bg-gray-700">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-all duration-200 ${
                    viewMode === 'grid'
                      ? 'bg-white text-sky-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Grid View"
                >
                  <FaTh />
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`p-2 rounded-md transition-all duration-200 ${
                    viewMode === 'table'
                      ? 'bg-white text-sky-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Table View"
                >
                  <FaTable />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-all duration-200 ${
                    viewMode === 'list'
                      ? 'bg-white text-sky-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="List View"
                >
                  <FaList />
                </button>
                <button
                  onClick={() => setViewMode('compact')}
                  className={`p-2 rounded-md transition-all duration-200 ${
                    viewMode === 'compact'
                      ? 'bg-white text-sky-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Compact View"
                >
                  <FaCompressArrowsAlt />
                </button>
              </div>
            </div>
            <SearchBar
              value={search}
              onChange={setSearch}
              placeholder="Tìm kiếm module..."
              className="w-80"
            />
          </div>
          <div className='p-6'>
            {renderContent()}
          </div>
        </div>

        {/* Dropdown Menu */}
        <Dropdown
          isOpen={isOpenOption}
          onClose={closeDropdown}
          className="fixed right-0 top-0 flex w-[200px] flex-col rounded-2xl border border-gray-200 bg-white p-3 shadow-lg dark:border-gray-800 dark:bg-gray-dark"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.right,
          }}
        >
          <ul className="flex flex-col gap-1 py-2">
            <li>
              <DropdownItem
                onItemClick={onClickEdit}
                tag="a"
                className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-sky-50 hover:text-sky-600 transition-all duration-300"
              >
                <FaEdit />
                Chỉnh sửa
              </DropdownItem>
            </li>
            <li>
              <DropdownItem
                onItemClick={onClickApprovalFlow}
                tag="a"
                className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-green-50 hover:text-green-600 transition-all duration-300"
              >
                <FaCog />
                Quy trình duyệt
              </DropdownItem>
            </li>
            <li>
              <DropdownItem
                onItemClick={() => setShowDeleteModal(true)}
                tag="a"
                className="flex items-center gap-3 px-3 py-2 font-medium text-gray-700 rounded-lg group text-theme-sm hover:bg-red-50 hover:text-red-600 transition-all duration-300"
              >
                <FaTrash />
                Xóa
              </DropdownItem>
            </li>
          </ul>
        </Dropdown>

      {/* Create Module Modal */}
      {showCreateModal && (
        <Modal isOpen={showCreateModal} onClose={() => { setShowCreateModal(false); resetForm(); }} className="max-w-[560px]">
          <div className="p-6 w-full">
            <h2 className="text-xl font-bold mb-4 text-gray-800">Thêm Module mới</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tên Module</label>
                <input
                  type="text"
                  value={itemName}
                  onChange={(e) => setItemName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-colors"
                  placeholder="Nhập tên module"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-colors"
                  placeholder="Nhập mô tả module"
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-4 mt-6">
                <Button
                  variant="outline"
                  onClick={() => { setShowCreateModal(false); resetForm(); }}
                >
                  Hủy bỏ
                </Button>
                <Button
                  variant="primary"
                  onClick={handleCreateItem}
                >
                  Thêm mới
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Approval Flow Modal */}
      {showApprovalFlowModal && (
        <Modal isOpen={showApprovalFlowModal} onClose={() => { setShowApprovalFlowModal(false); resetApprovalFlowForm(); }} className="max-w-[900px] max-h-[90vh] overflow-hidden">
          <div className="flex flex-col h-full max-h-[90vh]">
            {/* Header - Fixed */}
            <div className="approval-modal-header flex-shrink-0 p-6">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg">
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">
                    {existingFlowId ? 'Chỉnh sửa' : 'Cấu hình'} Quy trình Phê duyệt
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Module: <span className="font-medium text-blue-600">{selectedItem?.name}</span>
                  </p>
                </div>
              </div>
              {isLoadingFlow && (
                <div className="mt-4 flex items-center gap-2 text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg">
                  <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang tải thông tin quy trình...
                </div>
              )}
            </div>

            {/* Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-6">
              {/* Approval Flow Configuration */}
              <div className="mb-8">
                <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm mb-6">
                  <div className="flex items-center gap-2 mb-4">
                    <svg className="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="text-lg font-semibold text-gray-800">Thông tin cơ bản</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tên quy trình <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={flowName}
                        onChange={(e) => setFlowName(e.target.value)}
                        className={`approval-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400 ${isLoadingFlow ? 'approval-loading' : ''}`}
                        placeholder={`Approval Flow cho ${selectedItem?.name || 'Module'}`}
                        disabled={isLoadingFlow}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Mô tả quy trình <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={flowDescription}
                        onChange={(e) => setFlowDescription(e.target.value)}
                        className={`approval-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-400 ${isLoadingFlow ? 'approval-loading' : ''}`}
                        placeholder={`Quy trình phê duyệt cho module ${selectedItem?.name || ''}`}
                        disabled={isLoadingFlow}
                      />
                    </div>
                  </div>
                </div>

                {/* Approval Steps */}
                <div className="mt-6">
                  <div className="flex justify-between items-center mb-6">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-800">Các bước phê duyệt</h4>
                      <p className="text-sm text-gray-500 mt-1">Cấu hình quy trình phê duyệt cho module</p>
                    </div>
                    <button
                      onClick={addApprovalStep}
                      disabled={isLoadingFlow}
                      className="approval-btn-primary inline-flex items-center gap-2 px-4 py-2 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FaPlus className="h-4 w-4 approval-icon" />
                      Thêm bước
                    </button>
                  </div>

                  <div className="space-y-6">
                    {approvalSteps.map((step, stepIndex) => (
                      <div key={stepIndex} className="approval-step-card approval-fade-in relative bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                        {/* Step Header */}
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center gap-3">
                            <div className="approval-step-number flex items-center justify-center w-8 h-8 text-white text-sm font-semibold rounded-full">
                              {step.stepOrder}
                            </div>
                            <div>
                              <h5 className="font-semibold text-gray-800">Bước {step.stepOrder}</h5>
                              <p className="text-xs text-gray-500">Cấu hình thông tin bước phê duyệt</p>
                            </div>
                          </div>
                          {approvalSteps.length > 1 && (
                            <button
                              onClick={() => removeApprovalStep(stepIndex)}
                              disabled={isLoadingFlow}
                              className="approval-btn-danger inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <FaMinus className="h-3 w-3 approval-icon" />
                              Xóa bước
                            </button>
                          )}
                        </div>

                        {/* Step Name Input */}
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Tên bước <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={step.name}
                            onChange={(e) => updateApprovalStep(stepIndex, 'name', e.target.value)}
                            className="approval-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm placeholder-gray-400"
                            placeholder={`Nhập tên bước phê duyệt ${step.stepOrder}`}
                            disabled={isLoadingFlow}
                          />
                        </div>

                        {/* Step Approvers */}
                        <div>
                          <div className="flex justify-between items-center mb-3">
                            <label className="block text-sm font-medium text-gray-700">
                              Người phê duyệt <span className="text-red-500">*</span>
                            </label>
                            <button
                              onClick={() => addApprover(stepIndex)}
                              disabled={isLoadingFlow}
                              className="approval-btn-success inline-flex items-center gap-1 px-3 py-1.5 text-xs font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <FaPlus className="h-3 w-3 approval-icon" />
                              Thêm người duyệt
                            </button>
                          </div>

                          <div className="space-y-3">
                            {step.stepApprovers.map((approver, approverIndex) => (
                              <div key={approverIndex} className="approval-approver-card approval-slide-in flex gap-3 items-end p-4 rounded-lg">
                                <div className="flex-1">
                                  <label className="block text-xs font-medium text-gray-600 mb-1">Loại người duyệt</label>
                                  <div className="relative">
                                    <Select
                                    options={approverTypeOptions}
                                    defaultValue={approver.type}
                                    placeholder="Select an option"
                                    onChange={(e) => updateStepApprover(stepIndex, approverIndex, 'type', e)}
                                    className="dark:bg-dark-900"
                                  />
                                    <span className="absolute text-gray-500 -translate-y-1/2 pointer-events-none right-3 top-1/2 dark:text-gray-400">
                                      <ChevronDownIcon/>
                                    </span>
                                </div>
                                </div>
                                <div className="flex-1">
                                  <label className="block text-xs font-medium text-gray-600 mb-1">Tên người duyệt</label>
                                  <input
                                    type="text"
                                    value={approver.name}
                                    onChange={(e) => updateStepApprover(stepIndex, approverIndex, 'name', e.target.value)}
                                    className="approval-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm placeholder-gray-400"
                                    placeholder="Nhập tên người phê duyệt"
                                    disabled={isLoadingFlow}
                                  />
                                </div>
                                <div className="flex-1">
                                  <label className="block text-xs font-medium text-gray-600 mb-1">Email duyệt</label>
                                  <input
                                    type="text"
                                    value={approver.userId || ''}
                                    onChange={(e) => updateStepApprover(stepIndex, approverIndex, 'userId', e.target.value)}
                                    className="approval-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm placeholder-gray-400"
                                    placeholder="Nhập Email duyệt"
                                    disabled={
                                      isLoadingFlow || 
                                      approver.type !== 'UserApprove'
                                    }
                                  />
                                </div>
                                <button
                                    onClick={() => onClickAdditionalInfo(stepIndex, approverIndex)}
                                    disabled={isLoadingFlow}
                                    className="bg-blue-200 text-blue-800 inline-flex items-center justify-center w-8 h-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    <GrTableAdd className="h-3 w-3 approval-icon" />
                                </button>
                                
                                {step.stepApprovers.length > 1 && (
                                  <button
                                    onClick={() => removeApprover(stepIndex, approverIndex)}
                                    disabled={isLoadingFlow}
                                    className="approval-btn-danger inline-flex items-center justify-center w-8 h-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                  >
                                    <FaMinus className="h-3 w-3 approval-icon" />
                                  </button>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer - Fixed */}
            <div className="approval-modal-footer flex-shrink-0 p-6">
              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => { setShowApprovalFlowModal(false); resetApprovalFlowForm(); }}
                  disabled={isLoadingFlow}
                  className="approval-btn-secondary inline-flex items-center gap-2 px-6 py-3 text-gray-700 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Hủy bỏ
                </button>
                <button
                  onClick={handleApprovalFlowSubmit}
                  disabled={isLoadingFlow}
                  className="approval-btn-primary inline-flex items-center gap-2 px-6 py-3 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoadingFlow ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Đang xử lý...
                    </>
                  ) : (
                    <>
                      {existingFlowId ? (
                        <>
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                          Cập nhật Quy trình
                        </>
                      ) : (
                        <>
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Lưu Quy trình
                        </>
                      )}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </Modal>
      )}
      
      {showAdditionalInfoModal && (
        <Modal isOpen={showAdditionalInfoModal} onClose={() => setShowAdditionalInfoModal(false)} className="lg:max-w-4xl max-w-full">
          <div className="p-0 w-full">
            <AdditionalInfoTable
              data={[...approvalSteps][stepApprovalIndex].stepApprovers[stepApproverIndex].additionInfoSettings || []}
              stepId={[...approvalSteps][stepApprovalIndex].stepApprovers[stepApproverIndex].id}
              onDataChange={(newData) => {
                const newSteps = [...approvalSteps];
                newSteps[stepApprovalIndex].stepApprovers[stepApproverIndex].additionInfoSettings = newData;
                setApprovalSteps(newSteps);
              }}
            />
          </div>
        </Modal>
      )}

      {/* Update Module Modal */}
      {showUpdateModal && (
        <Modal isOpen={showUpdateModal} onClose={() => { setShowUpdateModal(false); resetForm(); }} className="max-w-[560px]">
          <div className="p-6 w-full">
            <h2 className="text-xl font-bold mb-4 text-gray-800">Cập nhật Module</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tên Module</label>
                <input
                  type="text"
                  value={itemName}
                  onChange={(e) => setItemName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-colors"
                  placeholder="Nhập tên module"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả</label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-transparent transition-colors"
                  placeholder="Nhập mô tả module"
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-4 mt-6">
                <Button
                  variant="outline"
                  onClick={() => { setShowUpdateModal(false); resetForm(); }}
                >
                  Hủy bỏ
                </Button>
                <Button
                  variant="primary"
                  onClick={handleUpdateItem}
                >
                  Cập nhật
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)} className="max-w-[560px]">
          <div className="mt-6 p-6 w-full text-center">
            <div className="mb-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaExclamation className="text-red-600 text-2xl" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Xác nhận xóa</h3>
            <p className="mb-6 text-gray-600">Bạn có chắc chắn muốn xóa Module này chứ?</p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
                Hủy bỏ
              </Button>
              <Button
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Đồng ý
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}