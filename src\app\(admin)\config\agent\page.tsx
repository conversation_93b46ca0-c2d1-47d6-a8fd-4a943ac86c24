/*
  Enhanced Agent Management Page with Beautiful UI
  Features: Modern design, animations, improved layout, visual effects
*/

'use client';

import './agent-animations.css';

import { agentApi } from '@/api/agentApi';
import PageBreadcrumb from '@/components/common/PageBreadCrumb';
import CreateAgentModal from '@/components/modals/CreateAgentModal';
import UpdateAgentModal from '@/components/modals/UpdateAgentModal';
import { Dropdown } from '@/components/ui/dropdown/Dropdown';
import { DropdownItem } from '@/components/ui/dropdown/DropdownItem';
import { Modal } from '@/components/ui/modal';
import SearchBar from '@/components/ui/search/SearchBar';
import { useModal } from '@/hooks/useModal';
import { Agent } from '@/types';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FaEye, FaTrash, FaExclamation, FaEllipsisV } from 'react-icons/fa';
import { RiRobot3Line } from "react-icons/ri";
import { TbMessageChatbot } from "react-icons/tb";
import { HiOutlineSparkles } from "react-icons/hi";
import { toast } from 'react-toastify';

const tabs = [
  { label: "All", icon: HiOutlineSparkles, count: 0, color: "from-blue-500 to-purple-600" },
  { label: "Chatbot", icon: TbMessageChatbot, count: 0, color: "from-green-500 to-blue-500" },
  { label: "Workflow", icon: RiRobot3Line, count: 0, color: "from-purple-500 to-pink-500" },
];

export default function AgentManagementPage() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [search, setSearch] = useState('');
  const [activeTab, setActiveTab] = useState("All");
  const [isCreated, setIsCreated] = useState<boolean>(false);
  const [isOpenOption, setIsOpenOption] = useState(false)
  const [openIndex, setOpenIndex] = useState(-1)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, left: 0 });

  // State để quản lý modal và agent được chọn
  const [agentSelected, setAgentSelected] = useState<Agent | null>(null);
  const [isViewingDetail, setIsViewingDetail] = useState(false); // Flag để biết đang xem chi tiết
  const { isOpen, openModal, closeModal } = useModal();
  const router = useRouter();

  useEffect(() => {
    if(isCreated){
      fetchAgents()
      setIsCreated(false)
    }
  }, [isCreated])

  // useEffect để mở modal khi đã set agent và flag isViewingDetail
  useEffect(() => {
    if(isViewingDetail && agentSelected) {
      openModal();
      setIsViewingDetail(false); // Reset flag
    }
  }, [isViewingDetail, agentSelected, openModal])

  const fetchAgents = async () => {
    try{
        const agents = await agentApi.getAgents(1000,0)
        if(agents != null)
        {
            setAgents(agents)
        }
    }
    catch(err)
    {
        console.log(err)
    }
  }
      
  useEffect(() => {
      
    fetchAgents()
  }, [])


  const onClickViewDetail = async () => {
    if(openIndex > -1)
    {
      const agent = agents[openIndex]
      // Set agent trước, sau đó set flag để báo hiệu đang xem chi tiết
      setAgentSelected(agent);
      setIsViewingDetail(true);

      setOpenIndex(-1);
      setIsOpenOption(false);
    }
  }

  const onClickDelete = () => {
    if(openIndex > -1)
    {
      setShowDeleteModal(true)
    }
  }
  const toggleDropdown = (e: any, index: number) => {
    e.preventDefault();
    const rect = e.currentTarget.getBoundingClientRect();
    console.log(rect)
    setDropdownPosition({
      top: rect.top,
      right: rect.right,
      left: rect.left
    });
    setOpenIndex(index);
    setIsOpenOption(true);
    console.log(index)
  };

  function closeDropdown() {
    setIsOpenOption(false);
  }

  const handleDelete = async () => {
    if(openIndex > -1)
    {
      try{
        const agent = agents[openIndex]
        if(agent){
          const res = await agentApi.deleteAgent(agent.id)
          if(res)
          {
            
            toast.success("Xóa agent thành công!")
            fetchAgents()
          }
        }
      }catch(err)
      {
        console.log(err)
      }
      
      setOpenIndex(-1);
      setIsOpenOption(false);
      setShowDeleteModal(false);
    }
  };

  // Calculate stats for tabs
  const getTabCounts = () => {
    const allCount = agents.length;
    const chatbotCount = agents.filter(agent => agent.type.toLowerCase() === 'chatbot').length;
    const workflowCount = agents.filter(agent => agent.type.toLowerCase() === 'workflow').length;

    return { allCount, chatbotCount, workflowCount };
  };

  const { allCount, chatbotCount, workflowCount } = getTabCounts();

  return (
    <div className="min-h-screen">
      <PageBreadcrumb pageTitle="Quản lý Agent" />

      {/* Stats Overview */}
      <div className="rounded-2xl border border-gray-200 bg-white shadow-sm dark:border-gray-800 dark:bg-white/[0.03]">
        <div className='p-6 border-b border-gray-100'>
          <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6'>
            <div className='flex flex-col sm:flex-row gap-6'>
              <UpdateAgentModal
                isOpen={isOpen}
                openModal={openModal}
                closeModal={closeModal}
                agent={agentSelected}
                onSuccess={fetchAgents}
              />

              {/* Enhanced Tabs */}
              <div className="flex items-center gap-2 bg-gray-100 p-1 rounded-xl h-11 dark:bg-gray-700">
                {tabs.map((tab) => {
                  const IconComponent = tab.icon;
                  const count = tab.label === 'All' ? allCount :
                              tab.label === 'Chatbot' ? chatbotCount : workflowCount;

                  return (
                    <button
                      key={tab.label}
                      onClick={() => setActiveTab(tab.label)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 relative overflow-hidden group
                        ${activeTab === tab.label
                          ? "bg-gray-50 text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-blue-600 hover:bg-white/50"}`}
                    >
                      <IconComponent className={`w-4 h-4 ${activeTab === tab.label ? 'text-blue-600' : 'dark:text-gray-300'}`} />
                      <span className={`${activeTab === tab.label ? 'text-blue-600' : 'dark:text-gray-300'} `}>{tab.label}</span>
                      <span className={`px-2 py-0.5 rounded-full text-xs font-semibold
                        ${activeTab === tab.label
                          ? "bg-blue-50 text-blue-600"
                          : "bg-gray-200 text-gray-600"}`}>
                        {count}
                      </span>
                    </button>
                  );
                })}
              </div>

              <CreateAgentModal isSuccess={isCreated} setIsSuccess={setIsCreated}/>
            </div>

            <SearchBar
              value={search}
              onChange={setSearch}
              placeholder="Tìm kiếm agent..."
              className="w-full lg:w-80"
            />
          </div>
        </div>

        {/* Agent Cards Section */}
        <div className='p-6'>
          {agents
            .filter((agent) => { if(activeTab.toLowerCase() == "all") return true; return activeTab.toLowerCase() == agent.type.toLowerCase()})
            .filter((agent) => { return agent.name.toLowerCase().includes(search.toLowerCase()) == true || search.length == 0})
            .length === 0 ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <HiOutlineSparkles className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Không tìm thấy Agent</h3>
              <p className="text-gray-500 mb-6">Không có agent nào phù hợp với bộ lọc hiện tại</p>
            </div>
          ) : (
            <div className='agent-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
              {agents
              .filter((agent) => { if(activeTab.toLowerCase() == "all") return true; return activeTab.toLowerCase() == agent.type.toLowerCase()})
              .filter((agent) => { return agent.name.toLowerCase().includes(search.toLowerCase()) == true || search.length == 0})
              .map((agent, index) => (
                  <div
                    key={agent.id || index}
                    className="agent-card group bg-white border border-gray-200 hover:border-blue-200 rounded-2xl shadow-sm hover:shadow-md p-6 cursor-pointer transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden dark:bg-gray-700 dark:border-gray-600"
                  >
                    {/* Subtle overlay effect */}
                    <div className="absolute inset-0 bg-blue-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>

                    <div className="relative z-10">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center flex-1" onClick={(e) => { e.preventDefault(); router.push(`/agent/${agent.code}`)}}>
                          <div className={`w-14 h-14 rounded-2xl flex items-center justify-center mr-4 transition-all duration-300 group-hover:scale-105
                            ${agent.type.toLowerCase() === 'workflow'
                              ? 'bg-purple-100'
                              : 'bg-blue-100'}`}>
                            {agent.type.toLowerCase() == 'workflow' ?
                              <RiRobot3Line className='h-7 w-7 text-purple-600'/> :
                              <TbMessageChatbot className='h-7 w-7 text-blue-600'/>}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 text-lg mb-1 group-hover:text-blue-600 transition-colors duration-300 dark:text-gray-50">{agent.name}</h3>
                            <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold uppercase tracking-wide
                              ${agent.type.toLowerCase() === 'workflow'
                                ? 'bg-purple-50 text-purple-600'
                                : 'bg-blue-50 text-blue-600'}`}>
                              {agent.type}
                            </div>
                          </div>
                        </div>
                        <button
                          className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 dropdown-toggle"
                          onClick={(e) => toggleDropdown(e, index)}
                        >
                          <FaEllipsisV className="text-gray-400 hover:text-gray-600 w-4 h-4"/>
                        </button>
                      </div>

                      <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3 dark:text-gray-300">{agent.description}</p>

                      {/* Action buttons */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span className="text-xs text-gray-500 font-medium dark:text-green-400">Hoạt động</span>
                        </div>
                        <button
                          onClick={() => { router.push(`/agent/${agent.code}`)}}
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium hover:underline transition-colors duration-200 dark:text-blue-400"
                        >
                          Truy cập →
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>
      {/* Enhanced Dropdown */}
      <Dropdown
        isOpen={isOpenOption}
        onClose={closeDropdown}
        className="dropdown-menu fixed right-0 top-0 flex w-[220px] flex-col rounded-2xl border border-gray-200 bg-white/95 backdrop-blur-sm p-2 shadow-2xl dark:border-gray-800 dark:bg-gray-dark"
        style={{
          top: dropdownPosition.top,
          left: dropdownPosition.right,
        }}
      >
        <ul className="flex flex-col gap-1 py-2">
          <li>
            <DropdownItem
              onItemClick={onClickViewDetail}
              tag="a"
              className="flex items-center gap-3 px-4 py-3 font-medium text-gray-700 rounded-xl group text-sm hover:bg-blue-50 hover:text-blue-700 transition-all duration-200"
            >
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-200">
                <FaEye className="w-4 h-4"/>
              </div>
              <span>Xem chi tiết</span>
            </DropdownItem>
          </li>
          <li>
            <DropdownItem
              onItemClick={onClickDelete}
              tag="a"
              className="flex items-center gap-3 px-4 py-3 font-medium text-gray-700 rounded-xl group text-sm hover:bg-red-50 hover:text-red-700 transition-all duration-200"
            >
              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors duration-200">
                <FaTrash className="w-4 h-4"/>
              </div>
              <span>Xóa Agent</span>
            </DropdownItem>
          </li>
        </ul>
      </Dropdown>

      {/* Enhanced Delete Modal */}
      {showDeleteModal && (
        <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)} className="max-w-[480px]">
          <div className="modal-content p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaExclamation className="text-red-600 text-2xl" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Xác nhận xóa Agent</h3>
            <p className="text-gray-600 mb-8 leading-relaxed">
              Bạn có chắc chắn muốn xóa Agent này không? Hành động này không thể hoàn tác.
            </p>
            <div className="flex justify-center space-x-4">
              <button
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors duration-200 font-medium"
                onClick={() => setShowDeleteModal(false)}
              >
                Hủy bỏ
              </button>
              <button
                className="px-6 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-colors duration-200 font-medium shadow-lg"
                onClick={handleDelete}
              >
                Xóa Agent
              </button>
            </div>
          </div>
        </Modal>
      )}
      </div>

    </div>
  );
}
